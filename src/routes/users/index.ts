import { describe<PERSON>oute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import z from "zod";

import db from "@/db";
import { insertUserSchema, selectUserSchema } from "@/db/schema";
import { jobVacancy, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { API_TAGS } from "@/lib/constants";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { listUsersDTO } from "@/lib/schemas";
import {
  paginationParamsSchema,
  patchUserSchema,
  userFilterSchema,
} from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { and, count, eq, like } from "drizzle-orm";

const createUserSchema = insertUserSchema
  .omit({
    id: true,
    role: true,
    emailVerified: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    banned: true,
    banReason: true,
    banExpires: true,
  })
  .extend({
    password: z.string().min(8),
  });

const changeRoleSchema = z.object({
  role: z.enum(["admin", "author", "editor"]),
});

const deleteAccountSchema = z.object({
  password: z.string().min(1, "Password is required"),
  deletionReason: z.string().min(1, "Deletion reason is required"),
});

export class UsersOperations {
  static async findMany(
    params: Partial<typeof userFilterSchema._type> & {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortDirection?: string;
    },
  ) {
    const {
      page = 1,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
      ...filters
    } = params || {};
    const offset = limit ? (page - 1) * limit : undefined;
    const [usersList, [{ total }]] = await Promise.all([
      db.query.user.findMany({
        columns: {
          id: true,
          name: true,
          description: true,
          email: true,
          image: true,
          phoneNumber: true,
          dateOfBirth: true,
          gender: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
        where: {
          ...(filters.name && { name: { like: `%${filters.name}%` } }),
          ...(filters.description && {
            description: { like: `%${filters.description}%` },
          }),
          ...(filters.email && { email: { like: `%${filters.email}%` } }),
          ...(filters.role && { role: filters.role }),
          ...(filters.gender && { gender: filters.gender }),
          ...(typeof filters.banned === "boolean" && {
            banned: filters.banned,
          }),
          ...(filters.createdAt && !Number.isNaN(Date.parse(filters.createdAt))
            ? { createdAt: { eq: new Date(filters.createdAt) } }
            : {}),
          ...(filters.updatedAt && !Number.isNaN(Date.parse(filters.updatedAt))
            ? { updatedAt: { eq: new Date(filters.updatedAt) } }
            : {}),
        },
        limit,
        offset,
        orderBy: { [sortBy]: sortDirection },
      }),
      db
        .select({ total: count() })
        .from(user)
        .where(
          and(
            filters.name ? like(user.name, `%${filters.name}%`) : undefined,
            filters.description
              ? like(user.description, `%${filters.description}%`)
              : undefined,
            filters.email ? like(user.email, `%${filters.email}%`) : undefined,
            filters.role ? eq(user.role, filters.role) : undefined,
            filters.gender ? eq(user.gender, filters.gender) : undefined,
            typeof filters.banned === "boolean"
              ? eq(user.banned, filters.banned)
              : undefined,
            filters.createdAt
              ? eq(user.createdAt, new Date(filters.createdAt))
              : undefined,
            filters.updatedAt
              ? eq(user.updatedAt, new Date(filters.updatedAt))
              : undefined,
          ),
        ),
    ]);
    return {
      items: usersList,
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
    };
  }

  static async findById(id: string) {
    return db.query.user.findFirst({
      where: { id },
      columns: {
        id: true,
        name: true,
        description: true,
        email: true,
        image: true,
        phoneNumber: true,
        dateOfBirth: true,
        gender: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  static async patch(
    id: string,
    updates: Partial<typeof patchUserSchema._type>,
  ) {
    const [updated] = await db
      .update(user)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(user.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db.delete(user).where(eq(user.id, id)).returning();
    return deleted;
  }

  static async deleteAccount(
    userId: string,
    password: string,
    deletionReason: string,
  ) {
    // First verify the password by attempting to sign in
    const userToDelete = await db.query.user.findFirst({
      where: { id: userId },
      columns: { email: true },
    });

    if (!userToDelete) {
      throw new Error("User not found");
    }

    // Verify password using better-auth sign-in
    try {
      const signInResult = await auth.api.signInEmail({
        body: {
          email: userToDelete.email,
          password: password,
        },
      });

      if (!signInResult.user || signInResult.user.id !== userId) {
        throw new Error("Invalid password");
      }
    } catch {
      throw new Error("Invalid password");
    }

    // Perform account deletion in a transaction
    return await db.transaction(async (tx) => {
      // Archive all published vacancies
      await tx
        .update(jobVacancy)
        .set({
          status: "archived",
          archivedAt: new Date(),
        })
        .where(
          and(
            eq(jobVacancy.authorId, userId),
            eq(jobVacancy.status, "published"),
          ),
        );

      // Delete all draft vacancies
      await tx
        .delete(jobVacancy)
        .where(
          and(eq(jobVacancy.authorId, userId), eq(jobVacancy.status, "draft")),
        );

      // Anonymize user data
      const [anonymizedUser] = await tx
        .update(user)
        .set({
          email: "",
          image: null,
          dateOfBirth: null,
          phoneNumber: null,
          name: "Deleted User",
          description: null,
          archivedAt: new Date(),
        })
        .where(eq(user.id, userId))
        .returning();

      return {
        success: true,
        deletionReason,
        deletedAt: new Date(),
        user: anonymizedUser,
      };
    });
  }
}

const router = createRouter()
  .use(authGuard)
  .get(
    "/",
    describeRoute({
      title: "List Users",
      description: "Get a paginated and filterable list of all users.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(listUsersDTO, "List of users"),
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator(
      "query",
      paginationParamsSchema.merge(userFilterSchema).optional(),
    ),
    async (c) => {
      const result = await UsersOperations.findMany(c.req.valid("query") || {});
      return c.json(result, HttpStatusCodes.OK);
    },
  )
  .get(
    "/me",
    describeRoute({
      title: "Get Current User",
      description: "Get the current user's information.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema,
          "Current user information",
        ),
        ...openApiResponses.unauthorized,
      },
    }),
    async (c) => {
      return c.json(c.var.checkedUser, HttpStatusCodes.OK);
    },
  )
  .post(
    "/editors",
    describeRoute({
      title: "Create Editor",
      description:
        "Create a new editor user. Requires editor creation permission.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          selectUserSchema,
          "Created editor user",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ editor: ["create"] }),
    validator("json", createUserSchema),
    async (c) => {
      const data = c.req.valid("json");
      const result = await auth.api.createUser({
        body: {
          ...data,
          role: "editor",
          emailVerified: true,
        },
        headers: c.req.raw.headers,
      });

      return c.json(result.user, HttpStatusCodes.CREATED);
    },
  )
  .patch(
    "/:id/role",
    describeRoute({
      title: "Change User Role",
      description: "Change a user's role. Requires role management permission.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ user: ["set-role"] }),
    validator("json", changeRoleSchema),
    async (c) => {
      const { id } = c.req.param();
      const { role } = c.req.valid("json");

      const result = await auth.api.setRole({
        body: {
          userId: id,
          role,
        },
        headers: c.req.raw.headers,
      });
      if (!result.user) {
        return responses.notFound(c, `User with ID ${id}`);
      }

      return c.json(result.user, HttpStatusCodes.OK);
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get User by ID",
      description: "Get a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema.pick({
            id: true,
            name: true,
            description: true,
            email: true,
            image: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            role: true,
            createdAt: true,
            updatedAt: true,
          }),
          "User details",
        ),
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      const { id } = c.req.valid("param");
      const found = await UsersOperations.findById(id);
      if (!found) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.json(found, HttpStatusCodes.OK);
    },
  )
  .patch(
    "/:id",
    describeRoute({
      title: "Update User",
      description: "Update a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ user: ["set-password"] }),
    validator("param", z.object({ id: z.string() })),
    validator("json", patchUserSchema),
    async (c) => {
      const { id } = c.req.valid("param");
      const updates = c.req.valid("json");
      const updated = await UsersOperations.patch(id, updates);
      if (!updated) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.json(updated, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/:id",
    describeRoute({
      title: "Delete User",
      description: "Delete a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: { description: "User deleted" },
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["delete"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      const { id } = c.req.valid("param");
      const deleted = await UsersOperations.delete(id);
      if (!deleted) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.body(null, HttpStatusCodes.NO_CONTENT);
    },
  )
  .post(
    "/delete-account",
    describeRoute({
      title: "Delete Account",
      description:
        "Delete the current user's account with password verification. This will archive all published vacancies and delete draft vacancies.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.object({
            success: z.boolean(),
            deletionReason: z.string(),
            deletedAt: z.date(),
            message: z.string(),
          }),
          "Account deletion confirmation",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    validator("json", deleteAccountSchema),
    async (c) => {
      const { password, deletionReason } = c.req.valid("json");
      const userId = c.var.checkedUser.id;

      try {
        const result = await UsersOperations.deleteAccount(
          userId,
          password,
          deletionReason,
        );
        return c.json(
          {
            success: result.success,
            deletionReason: result.deletionReason,
            deletedAt: result.deletedAt,
            message:
              "Account has been successfully deleted and data anonymized.",
          },
          HttpStatusCodes.OK,
        );
      } catch (error) {
        return responses.unprocessableEntity(c, (error as Error).message);
      }
    },
  );
// .route("/authors", authors); TODO FIX

export default router;
